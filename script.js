// Global variables
let currentData = [];
let analysisResults = {};
let missingDataChart = null;
let completenessChart = null;
let dataWorker = null;
let currentPage = 1;
let itemsPerPage = 20;
let filteredData = [];
let sortColumn = null;
let sortDirection = 'asc';

// Expected KYC fields
const kycFields = [
    'CIF', 'CONTACT_NO', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'NO_OF_PTO',
    'OCCUPATION', 'INDUSTRY_SECTOR', 'CUSTOMER_SEGMENT', 'CUSTOMER_SEGMENT_UDF',
    'INCOME_LEVEL', 'HOME_ADDRESS', 'MAILING_ADDRESS', 'ADD01', 'DEFAULT',
    'OFFICE_ADDRESS', 'EMPLOYMENTSTATUS', 'DATE_OF_BIRTH', 'NATIONALITY',
    'FATHERNAME', 'NO_OF_SIGNATURE'
];

// Critical fields for AML compliance
const criticalFields = [
    'CIF', 'FULL_NAME', 'NATIONAL_IDENTIFIER', 'DATE_OF_BIRTH',
    'NATIONALITY', 'HOME_ADDRESS', 'OCCUPATION'
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupFileUpload();
    setupTableControls();
    initializeWorker();
});

function initializeWorker() {
    // Disable Web Workers for file:// protocol compatibility
    // All processing will happen in main thread with progress updates
    dataWorker = null;
    console.log('✅ Application initialized for file:// protocol - Main thread processing enabled');
}

// Web Worker handlers removed - using main thread processing only

function updateProgressIndicator(stage, progress, message) {
    // Update overall progress
    const stages = ['upload', 'parse', 'validate', 'analyze'];
    const stageIndex = stages.indexOf(stage);
    const overallProgress = (stageIndex * 25) + (progress * 0.25);

    document.getElementById('overallProgress').style.width = overallProgress + '%';
    document.getElementById('overallProgressText').textContent = message;

    // Update stage-specific progress
    const stageElement = document.getElementById(stage + 'Stage');
    const progressElement = document.getElementById(stage + 'Progress');
    const textElement = document.getElementById(stage + 'Text');

    if (stageElement && progressElement && textElement) {
        progressElement.style.width = progress + '%';
        textElement.textContent = message;

        // Update stage status
        stageElement.classList.remove('active', 'completed');
        if (progress === 100) {
            stageElement.classList.add('completed');
        } else if (progress > 0) {
            stageElement.classList.add('active');
        }
    }

    // Update estimated time
    if (overallProgress > 0 && overallProgress < 100) {
        const estimatedTime = calculateEstimatedTime(overallProgress);
        document.getElementById('estimatedTime').textContent = `Est. ${estimatedTime}`;
    }
}

function calculateEstimatedTime(progress) {
    // Simple estimation based on current progress
    const elapsed = Date.now() - (window.processingStartTime || Date.now());
    const remaining = (elapsed / progress) * (100 - progress);

    if (remaining < 60000) {
        return Math.round(remaining / 1000) + 's remaining';
    } else {
        return Math.round(remaining / 60000) + 'm remaining';
    }
}

function showProgressIndicators() {
    document.getElementById('progressSection').style.display = 'block';
    document.getElementById('loadingOverlay').style.display = 'flex';
    window.processingStartTime = Date.now();
}

function hideProgressIndicators() {
    document.getElementById('progressSection').style.display = 'none';
    document.getElementById('loadingOverlay').style.display = 'none';
}

function setupFileUpload() {
    const fileInput = document.getElementById('fileInput');
    const fileUploadArea = document.getElementById('fileUploadArea');

    // Handle file selection
    fileInput.addEventListener('change', handleFileSelect);

    // Handle drag and drop
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#764ba2';
        fileUploadArea.style.backgroundColor = '#f8f9ff';
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#667eea';
        fileUploadArea.style.backgroundColor = '';
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.style.borderColor = '#667eea';
        fileUploadArea.style.backgroundColor = '';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    if (!file) return;

    // Validate file size (200MB limit)
    const maxSize = 200 * 1024 * 1024; // 200MB
    const fileSizeMB = file.size / (1024 * 1024);

    if (file.size > maxSize) {
        showErrorMessage(`File size (${fileSizeMB.toFixed(1)}MB) exceeds the maximum limit of 200MB. Please use a smaller file.`);
        return;
    }

    // Show warning for large Excel files
    if (file.name.match(/\.(xlsx|xls)$/i) && fileSizeMB > 50) {
        showLargeFileWarning(fileSizeMB);
    }

    // Validate file type
    const validExtensions = ['.csv', '.xlsx', '.xls'];
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
    if (!validExtensions.includes(fileExtension)) {
        showErrorMessage('Please upload a CSV or Excel file (.csv, .xlsx, .xls)');
        return;
    }

    // Display file info
    displayFileInfo(file);

    // Show progress indicators
    showProgressIndicators();

    // Start processing timeout
    startProcessingTimeout();

    // Hide analysis section
    document.getElementById('analysisSection').style.display = 'none';

    // Processing in main thread with chunked processing to maintain UI responsiveness
    console.log('Processing file in main thread with optimized chunking...');

    // Process file based on type
    if (file.name.endsWith('.csv')) {
        processLargeCSV(file);
    } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        processLargeExcel(file);
    } else {
        showErrorMessage('Please upload a CSV or Excel file.');
        hideProgressIndicators();
    }
}

function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fef2f2;
        border: 1px solid #fca5a5;
        color: #dc2626;
        padding: 20px;
        border-radius: 8px;
        z-index: 1001;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        white-space: pre-line;
    `;
    errorDiv.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #dc2626;"></i>
        </div>
        <div style="margin-bottom: 15px; font-weight: 500;">${message}</div>
        <button onclick="this.parentElement.remove()" style="background: #dc2626; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">OK</button>
    `;
    document.body.appendChild(errorDiv);

    // Auto-hide after 8 seconds for longer messages
    setTimeout(() => {
        if (errorDiv.parentElement) {
            errorDiv.remove();
        }
    }, 8000);
}

function showLargeFileWarning(fileSizeMB) {
    const warningDiv = document.createElement('div');
    warningDiv.style.cssText = `
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fef3c7;
        border: 1px solid #f59e0b;
        color: #92400e;
        padding: 20px;
        border-radius: 8px;
        z-index: 1000;
        max-width: 500px;
        text-align: center;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    `;
    warningDiv.innerHTML = `
        <div style="margin-bottom: 15px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: #f59e0b;"></i>
        </div>
        <div style="margin-bottom: 15px; font-weight: 500;">
            <strong>Large Excel File Warning</strong><br>
            File size: ${fileSizeMB.toFixed(1)}MB<br><br>
            Large Excel files may cause memory issues. For better performance, consider converting to CSV format.
        </div>
        <div style="display: flex; gap: 10px; justify-content: center;">
            <button onclick="this.parentElement.remove()" style="background: #f59e0b; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Continue Anyway</button>
            <button onclick="this.parentElement.remove()" style="background: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Cancel</button>
        </div>
    `;
    document.body.appendChild(warningDiv);

    // Auto-hide after 10 seconds
    setTimeout(() => {
        if (warningDiv.parentElement) {
            warningDiv.remove();
        }
    }, 10000);
}

function processLargeCSV(file) {
    const reader = new FileReader();

    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const progress = Math.round((e.loaded / e.total) * 100);
            updateProgressIndicator('upload', progress, `Uploading... ${progress}%`);
        }
    };

    reader.onload = function(e) {
        updateProgressIndicator('upload', 100, 'Upload complete');

        // Process CSV data directly in main thread
        setTimeout(() => {
            processCSVFallback(e.target.result, file.name);
        }, 100); // Small delay to allow UI update
    };

    reader.onerror = function() {
        hideProgressIndicators();
        alert('Error reading file. Please try again.');
    };

    reader.readAsText(file);
}

function processLargeExcel(file) {
    // Additional size check for Excel files (they use more memory)
    const fileSizeMB = file.size / (1024 * 1024);

    if (fileSizeMB > 100) {
        showErrorMessage(`Excel file is ${fileSizeMB.toFixed(1)}MB. For files over 100MB, please convert to CSV format for better performance and reliability.`);
        return;
    }

    const reader = new FileReader();

    reader.onprogress = function(e) {
        if (e.lengthComputable) {
            const progress = Math.round((e.loaded / e.total) * 100);
            updateProgressIndicator('upload', progress, `Uploading... ${progress}%`);
        }
    };

    reader.onload = function(e) {
        updateProgressIndicator('upload', 100, 'Upload complete');

        // Check if the array buffer is valid
        if (!e.target.result || e.target.result.byteLength === 0) {
            hideProgressIndicators();
            showErrorMessage('File appears to be empty or corrupted. Please try a different file.');
            return;
        }

        // Process Excel data directly in main thread
        setTimeout(() => {
            processExcelFallback(e.target.result, file.name);
        }, 100); // Small delay to allow UI update
    };

    reader.onerror = function(error) {
        console.error('FileReader error:', error);
        hideProgressIndicators();
        showErrorMessage('Error reading file. The file may be corrupted or too large. Please try again with a different file.');
    };

    // Add timeout for very large files
    setTimeout(() => {
        if (reader.readyState === FileReader.LOADING) {
            reader.abort();
            hideProgressIndicators();
            showErrorMessage('File reading timeout. The file may be too large. Please try with a smaller file or convert to CSV format.');
        }
    }, 30000); // 30 second timeout

    reader.readAsArrayBuffer(file);
}

function handleAnalysisComplete(results) {
    try {
        console.log('Handling analysis completion...', results);

        // Clear any processing timeout
        clearProcessingTimeout();

        analysisResults = results;
        currentData = []; // Clear raw data to save memory

        // Update UI
        console.log('Updating statistics...');
        updateStatistics();

        console.log('Updating field analysis table...');
        updateFieldAnalysisTable();

        console.log('Creating charts...');
        createCharts();

        console.log('Creating risk assessment...');
        createRiskAssessment();

        // Hide progress and show results
        hideProgressIndicators();

        const analysisSection = document.getElementById('analysisSection');
        if (analysisSection) {
            analysisSection.style.display = 'block';
            console.log('Analysis section displayed');

            // Scroll to results
            analysisSection.scrollIntoView({
                behavior: 'smooth'
            });
        } else {
            console.error('Analysis section not found');
        }

        console.log('Analysis complete and results displayed successfully');

    } catch (error) {
        console.error('Error in handleAnalysisComplete:', error);
        hideProgressIndicators();
        showErrorMessage('Error displaying results: ' + error.message);
    }
}

function displayFileInfo(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileInfo').style.display = 'block';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function processCSV(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const csv = e.target.result;
        const lines = csv.split('\n');
        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        
        const data = [];
        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = parseCSVLine(lines[i]);
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });
                data.push(row);
            }
        }
        
        currentData = data;
        document.getElementById('recordCount').textContent = data.length;
        analyzeData();
    };
    reader.readAsText(file);
}

function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    result.push(current.trim());
    return result;
}

function processExcel(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        currentData = jsonData;
        document.getElementById('recordCount').textContent = jsonData.length;
        analyzeData();
    };
    reader.readAsArrayBuffer(file);
}

function analyzeData() {
    if (currentData.length === 0) {
        alert('No data found in the file.');
        document.getElementById('loading').style.display = 'none';
        return;
    }

    // Get all available fields from the data
    const availableFields = Object.keys(currentData[0]);
    
    // Analyze missing data for each field
    const fieldAnalysis = {};
    const totalRecords = currentData.length;
    
    availableFields.forEach(field => {
        const blankCount = currentData.filter(row => 
            !row[field] || 
            row[field].toString().trim() === '' || 
            row[field].toString().toLowerCase() === 'null' ||
            row[field].toString().toLowerCase() === 'undefined'
        ).length;
        
        const blankPercentage = (blankCount / totalRecords) * 100;
        
        fieldAnalysis[field] = {
            totalRecords,
            blankCount,
            blankPercentage: blankPercentage.toFixed(2),
            riskLevel: getRiskLevel(field, blankPercentage)
        };
    });

    // Calculate profile completeness
    const profileCompleteness = currentData.map(row => {
        const filledFields = availableFields.filter(field => 
            row[field] && 
            row[field].toString().trim() !== '' && 
            row[field].toString().toLowerCase() !== 'null' &&
            row[field].toString().toLowerCase() !== 'undefined'
        ).length;
        return (filledFields / availableFields.length) * 100;
    });

    const completeProfiles = profileCompleteness.filter(p => p === 100).length;
    const incompleteProfiles = totalRecords - completeProfiles;
    const completionRate = ((completeProfiles / totalRecords) * 100).toFixed(1);

    // Store results
    analysisResults = {
        fieldAnalysis,
        profileCompleteness,
        totalRecords,
        completeProfiles,
        incompleteProfiles,
        completionRate,
        availableFields
    };

    // Update UI
    updateStatistics();
    updateFieldAnalysisTable();
    createCharts();
    createRiskAssessment();

    // Show results
    document.getElementById('loading').style.display = 'none';
    document.getElementById('analysisSection').style.display = 'block';
}

function getRiskLevel(field, blankPercentage) {
    const isCritical = criticalFields.includes(field);
    
    if (isCritical) {
        if (blankPercentage > 10) return 'HIGH';
        if (blankPercentage > 5) return 'MEDIUM';
        return 'LOW';
    } else {
        if (blankPercentage > 25) return 'HIGH';
        if (blankPercentage > 15) return 'MEDIUM';
        return 'LOW';
    }
}

function updateStatistics() {
    document.getElementById('totalRecords').textContent = analysisResults.totalRecords.toLocaleString();
    document.getElementById('completeProfiles').textContent = analysisResults.completeProfiles.toLocaleString();
    document.getElementById('incompleteProfiles').textContent = analysisResults.incompleteProfiles.toLocaleString();
    document.getElementById('completionRate').textContent = analysisResults.completionRate + '%';
}

function updateFieldAnalysisTable() {
    // Initialize filtered data
    filteredData = Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage);

    // Update the table with pagination
    updateTable();
}

function createCharts() {
    createMissingDataChart();
    createCompletenessChart();
}

function createMissingDataChart() {
    const ctx = document.getElementById('missingDataChart').getContext('2d');

    if (missingDataChart) {
        missingDataChart.destroy();
    }

    const fieldData = Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
        .slice(0, 10); // Top 10 fields with most missing data

    const labels = fieldData.map(([field]) => field);
    const data = fieldData.map(([, analysis]) => parseFloat(analysis.blankPercentage));
    const colors = fieldData.map(([field, analysis]) => {
        if (analysis.riskLevel === 'HIGH') return '#ff6b6b';
        if (analysis.riskLevel === 'MEDIUM') return '#ffa726';
        return '#66bb6a';
    });

    missingDataChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Missing Data %',
                data: data,
                backgroundColor: colors,
                borderColor: colors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function createCompletenessChart() {
    const ctx = document.getElementById('completenessChart').getContext('2d');

    if (completenessChart) {
        completenessChart.destroy();
    }

    // Create completeness distribution
    const ranges = [
        { label: '0-25%', min: 0, max: 25, color: '#ff6b6b' },
        { label: '26-50%', min: 26, max: 50, color: '#ffa726' },
        { label: '51-75%', min: 51, max: 75, color: '#ffca28' },
        { label: '76-99%', min: 76, max: 99, color: '#66bb6a' },
        { label: '100%', min: 100, max: 100, color: '#4caf50' }
    ];

    const distribution = ranges.map(range => {
        return analysisResults.profileCompleteness.filter(completeness =>
            completeness >= range.min && completeness <= range.max
        ).length;
    });

    completenessChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ranges.map(r => r.label),
            datasets: [{
                data: distribution,
                backgroundColor: ranges.map(r => r.color),
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function createRiskAssessment() {
    const riskGrid = document.getElementById('riskGrid');
    riskGrid.innerHTML = '';

    // High risk fields
    const highRiskFields = Object.entries(analysisResults.fieldAnalysis)
        .filter(([, analysis]) => analysis.riskLevel === 'HIGH');

    if (highRiskFields.length > 0) {
        const highRiskCard = document.createElement('div');
        highRiskCard.className = 'risk-card';
        highRiskCard.style.borderLeftColor = '#c62828';
        highRiskCard.innerHTML = `
            <h4 style="color: #c62828; margin-bottom: 10px;">🚨 High Risk Fields</h4>
            <p><strong>${highRiskFields.length}</strong> fields require immediate attention:</p>
            <ul style="margin-top: 10px;">
                ${highRiskFields.map(([field, analysis]) =>
                    `<li>${field}: ${analysis.blankPercentage}% missing</li>`
                ).join('')}
            </ul>
        `;
        riskGrid.appendChild(highRiskCard);
    }

    // Critical fields assessment
    const criticalFieldsAnalysis = criticalFields
        .filter(field => analysisResults.fieldAnalysis[field])
        .map(field => ({
            field,
            ...analysisResults.fieldAnalysis[field]
        }));

    const criticalCard = document.createElement('div');
    criticalCard.className = 'risk-card';
    criticalCard.style.borderLeftColor = '#1976d2';
    criticalCard.innerHTML = `
        <h4 style="color: #1976d2; margin-bottom: 10px;">🔍 Critical AML Fields Status</h4>
        <ul style="margin-top: 10px;">
            ${criticalFieldsAnalysis.map(analysis =>
                `<li>${analysis.field}: ${analysis.blankPercentage}% missing
                 <span class="risk-badge risk-${analysis.riskLevel.toLowerCase()}">${analysis.riskLevel}</span></li>`
            ).join('')}
        </ul>
    `;
    riskGrid.appendChild(criticalCard);

    // Compliance summary
    const complianceCard = document.createElement('div');
    complianceCard.className = 'risk-card';
    complianceCard.style.borderLeftColor = '#388e3c';
    complianceCard.innerHTML = `
        <h4 style="color: #388e3c; margin-bottom: 10px;">📊 Compliance Summary</h4>
        <p><strong>Profile Completion Rate:</strong> ${analysisResults.completionRate}%</p>
        <p><strong>Complete Profiles:</strong> ${analysisResults.completeProfiles.toLocaleString()}</p>
        <p><strong>Profiles Needing Review:</strong> ${analysisResults.incompleteProfiles.toLocaleString()}</p>
        <p style="margin-top: 10px; font-size: 0.9em; color: #666;">
            ${analysisResults.completionRate >= 90 ? '✅ Good compliance level' :
              analysisResults.completionRate >= 75 ? '⚠️ Moderate compliance - improvement needed' :
              '❌ Poor compliance - immediate action required'}
        </p>
    `;
    riskGrid.appendChild(complianceCard);
}

// Export functions
function exportToCSV() {
    const csvContent = generateAnalysisCSV();
    downloadFile(csvContent, 'kyc-analysis-report.csv', 'text/csv');
}

function generateAnalysisCSV() {
    let csv = 'Field Name,Total Records,Blank Count,Blank Percentage,Risk Level\n';

    Object.entries(analysisResults.fieldAnalysis)
        .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
        .forEach(([field, analysis]) => {
            csv += `"${field}",${analysis.totalRecords},${analysis.blankCount},${analysis.blankPercentage}%,${analysis.riskLevel}\n`;
        });

    return csv;
}

function exportToPDF() {
    // Create a comprehensive report
    const reportContent = generateHTMLReport();

    // Open in new window for printing/PDF
    const printWindow = window.open('', '_blank');
    printWindow.document.write(reportContent);
    printWindow.document.close();
    printWindow.focus();

    // Trigger print dialog
    setTimeout(() => {
        printWindow.print();
    }, 500);
}

function generateHTMLReport() {
    const currentDate = new Date().toLocaleDateString();
    const currentTime = new Date().toLocaleTimeString();

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <title>KYC Data Analysis Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { text-align: center; margin-bottom: 30px; }
            .summary { background: #f5f5f5; padding: 20px; margin: 20px 0; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            th { background-color: #f2f2f2; }
            .risk-high { background-color: #ffebee; }
            .risk-medium { background-color: #fff3e0; }
            .risk-low { background-color: #e8f5e8; }
            .page-break { page-break-before: always; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>KYC Data Analysis Report</h1>
            <p>Generated on ${currentDate} at ${currentTime}</p>
        </div>

        <div class="summary">
            <h2>Executive Summary</h2>
            <p><strong>Total Records Analyzed:</strong> ${analysisResults.totalRecords.toLocaleString()}</p>
            <p><strong>Complete Profiles:</strong> ${analysisResults.completeProfiles.toLocaleString()}</p>
            <p><strong>Incomplete Profiles:</strong> ${analysisResults.incompleteProfiles.toLocaleString()}</p>
            <p><strong>Overall Completion Rate:</strong> ${analysisResults.completionRate}%</p>
        </div>

        <h2>Field Analysis</h2>
        <table>
            <thead>
                <tr>
                    <th>Field Name</th>
                    <th>Total Records</th>
                    <th>Blank Count</th>
                    <th>Blank Percentage</th>
                    <th>Risk Level</th>
                </tr>
            </thead>
            <tbody>
                ${Object.entries(analysisResults.fieldAnalysis)
                    .sort((a, b) => b[1].blankPercentage - a[1].blankPercentage)
                    .map(([field, analysis]) => `
                        <tr class="risk-${analysis.riskLevel.toLowerCase()}">
                            <td>${field}</td>
                            <td>${analysis.totalRecords.toLocaleString()}</td>
                            <td>${analysis.blankCount.toLocaleString()}</td>
                            <td>${analysis.blankPercentage}%</td>
                            <td>${analysis.riskLevel}</td>
                        </tr>
                    `).join('')}
            </tbody>
        </table>

        <div class="page-break">
            <h2>Risk Assessment</h2>
            <h3>High Risk Fields</h3>
            ${Object.entries(analysisResults.fieldAnalysis)
                .filter(([, analysis]) => analysis.riskLevel === 'HIGH')
                .map(([field, analysis]) => `<p>• ${field}: ${analysis.blankPercentage}% missing data</p>`)
                .join('') || '<p>No high-risk fields identified.</p>'}

            <h3>Recommendations</h3>
            <ul>
                <li>Prioritize data collection for high-risk fields</li>
                <li>Implement data validation rules for critical AML fields</li>
                <li>Review and update customer onboarding processes</li>
                <li>Consider automated follow-up for incomplete profiles</li>
            </ul>
        </div>
    </body>
    </html>
    `;
}

function exportIncompleteRecords() {
    const incompleteRecords = currentData.filter((row, index) =>
        analysisResults.profileCompleteness[index] < 100
    );

    if (incompleteRecords.length === 0) {
        alert('No incomplete records found!');
        return;
    }

    const headers = analysisResults.availableFields;
    let csv = headers.join(',') + '\n';

    incompleteRecords.forEach(row => {
        const values = headers.map(header => {
            const value = row[header] || '';
            return `"${value.toString().replace(/"/g, '""')}"`;
        });
        csv += values.join(',') + '\n';
    });

    downloadFile(csv, 'incomplete-kyc-records.csv', 'text/csv');
}

function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
}

function setupTableControls() {
    // Search functionality
    const searchInput = document.getElementById('tableSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            filterTable(e.target.value);
        });
    }

    // Filter functionality
    const filterSelect = document.getElementById('riskFilter');
    if (filterSelect) {
        filterSelect.addEventListener('change', function(e) {
            filterByRisk(e.target.value);
        });
    }

    // Items per page
    const itemsSelect = document.getElementById('itemsPerPage');
    if (itemsSelect) {
        itemsSelect.addEventListener('change', function(e) {
            itemsPerPage = parseInt(e.target.value);
            currentPage = 1;
            updateTable();
        });
    }
}

function filterTable(searchTerm) {
    if (!analysisResults.fieldAnalysis) return;

    const allFields = Object.entries(analysisResults.fieldAnalysis);

    if (searchTerm.trim() === '') {
        filteredData = allFields;
    } else {
        filteredData = allFields.filter(([field, analysis]) =>
            field.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }

    currentPage = 1;
    updateTable();
}

function filterByRisk(riskLevel) {
    if (!analysisResults.fieldAnalysis) return;

    const allFields = Object.entries(analysisResults.fieldAnalysis);

    if (riskLevel === 'ALL') {
        filteredData = allFields;
    } else {
        filteredData = allFields.filter(([field, analysis]) =>
            analysis.riskLevel === riskLevel
        );
    }

    currentPage = 1;
    updateTable();
}

function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    filteredData.sort((a, b) => {
        let aVal, bVal;

        switch (column) {
            case 'field':
                aVal = a[0];
                bVal = b[0];
                break;
            case 'blank':
                aVal = a[1].blankCount;
                bVal = b[1].blankCount;
                break;
            case 'percentage':
                aVal = a[1].blankPercentage;
                bVal = b[1].blankPercentage;
                break;
            case 'risk':
                const riskOrder = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
                aVal = riskOrder[a[1].riskLevel];
                bVal = riskOrder[b[1].riskLevel];
                break;
            default:
                return 0;
        }

        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }

        if (sortDirection === 'asc') {
            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        } else {
            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
        }
    });

    updateTable();
    updateSortIndicators();
}

function updateSortIndicators() {
    // Remove all sort indicators
    document.querySelectorAll('.sortable i').forEach(icon => {
        icon.className = 'fas fa-sort';
    });

    // Add current sort indicator
    if (sortColumn) {
        const header = document.querySelector(`[onclick="sortTable('${sortColumn}')"] i`);
        if (header) {
            header.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        }
    }
}

function updateTable() {
    if (!filteredData.length) return;

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    const tbody = document.getElementById('fieldAnalysisBody');
    tbody.innerHTML = '';

    pageData.forEach(([field, analysis]) => {
        const row = document.createElement('tr');
        row.className = `risk-${analysis.riskLevel.toLowerCase()}`;

        row.innerHTML = `
            <td><strong>${field}</strong></td>
            <td>${analysis.totalRecords.toLocaleString()}</td>
            <td>${analysis.blankCount.toLocaleString()}</td>
            <td>${analysis.blankPercentage}%</td>
            <td><span class="risk-badge risk-${analysis.riskLevel.toLowerCase()}">${analysis.riskLevel}</span></td>
        `;

        tbody.appendChild(row);
    });

    updatePagination();
}

function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, filteredData.length);

    // Update pagination info
    document.getElementById('paginationInfo').textContent =
        `Showing ${startItem}-${endItem} of ${filteredData.length} entries`;

    // Update page numbers
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    // Previous button
    const prevBtn = document.createElement('button');
    prevBtn.className = 'page-number';
    prevBtn.textContent = '‹';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => changePage(currentPage - 1);
    pageNumbers.appendChild(prevBtn);

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `page-number ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.onclick = () => changePage(i);
        pageNumbers.appendChild(pageBtn);
    }

    // Next button
    const nextBtn = document.createElement('button');
    nextBtn.className = 'page-number';
    nextBtn.textContent = '›';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => changePage(currentPage + 1);
    pageNumbers.appendChild(nextBtn);
}

function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        updateTable();
    }
}

// Fallback functions for main thread processing
function processCSVFallback(csvText, fileName) {
    try {
        console.log('Starting CSV processing...');
        updateProgressIndicator('parse', 0, 'Parsing CSV data...');

        const lines = csvText.split('\n').filter(line => line.trim()); // Remove empty lines
        console.log(`Found ${lines.length} lines in CSV`);

        if (lines.length === 0) {
            throw new Error('CSV file appears to be empty');
        }

        const headers = parseCSVLine(lines[0]);
        console.log('Headers:', headers);

        if (!headers || headers.length === 0) {
            throw new Error('No headers found in CSV file');
        }

        const data = [];

        // Process all data at once for smaller files, or in chunks for larger files
        if (lines.length < 1000) {
            // Process small files immediately
            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = parseCSVLine(lines[i]);
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header.trim().replace(/"/g, '')] = values[index] || '';
                    });
                    data.push(row);
                }
            }

            console.log(`Processed ${data.length} rows immediately`);
            updateProgressIndicator('parse', 100, 'CSV parsing complete');
            setTimeout(() => analyzeDataFallback(data), 100);

        } else {
            // Process large files in chunks
            let currentIndex = 1;

            function processChunk() {
                const chunkSize = 500;
                const endIndex = Math.min(currentIndex + chunkSize, lines.length);

                console.log(`Processing chunk: ${currentIndex} to ${endIndex}`);

                for (let i = currentIndex; i < endIndex; i++) {
                    if (lines[i].trim()) {
                        const values = parseCSVLine(lines[i]);
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header.trim().replace(/"/g, '')] = values[index] || '';
                        });
                        data.push(row);
                    }
                }

                currentIndex = endIndex;
                const progress = Math.round((currentIndex / lines.length) * 100);
                updateProgressIndicator('parse', progress, `Parsing row ${currentIndex} of ${lines.length}`);

                if (currentIndex < lines.length) {
                    setTimeout(processChunk, 10);
                } else {
                    console.log(`CSV parsing complete. Processed ${data.length} rows`);
                    updateProgressIndicator('parse', 100, 'CSV parsing complete');
                    setTimeout(() => analyzeDataFallback(data), 100);
                }
            }

            setTimeout(processChunk, 100);
        }

    } catch (error) {
        console.error('CSV processing error:', error);
        hideProgressIndicators();
        showErrorMessage('Error processing CSV file: ' + error.message);
    }
}

function processExcelFallback(arrayBuffer, fileName) {
    try {
        updateProgressIndicator('parse', 0, 'Parsing Excel data...');

        // Check file size and available memory
        const fileSizeMB = arrayBuffer.byteLength / (1024 * 1024);
        console.log(`Processing Excel file: ${fileSizeMB.toFixed(2)}MB`);

        setTimeout(() => {
            try {
                updateProgressIndicator('parse', 10, 'Reading Excel file structure...');

                // Use more memory-efficient options for large files
                const readOptions = {
                    type: 'array',
                    cellDates: false,
                    cellNF: false,
                    cellStyles: false,
                    sheetStubs: false,
                    bookDeps: false,
                    bookFiles: false,
                    bookProps: false,
                    bookSheets: false,
                    bookVBA: false
                };

                // For very large files, try reading with minimal options
                if (fileSizeMB > 50) {
                    readOptions.dense = true; // Use dense mode for large files
                    updateProgressIndicator('parse', 15, 'Using optimized mode for large file...');
                }

                const workbook = XLSX.read(arrayBuffer, readOptions);
                updateProgressIndicator('parse', 40, 'Excel workbook loaded successfully');

                if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                    throw new Error('No worksheets found in Excel file');
                }

                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                if (!worksheet) {
                    throw new Error('Unable to read worksheet data');
                }

                updateProgressIndicator('parse', 60, 'Converting Excel data to JSON...');

                setTimeout(() => {
                    try {
                        // Convert with memory-efficient options
                        const jsonOptions = {
                            header: 1,
                            defval: '',
                            blankrows: false
                        };

                        const rawData = XLSX.utils.sheet_to_json(worksheet, jsonOptions);

                        if (!rawData || rawData.length === 0) {
                            throw new Error('No data found in Excel worksheet');
                        }

                        updateProgressIndicator('parse', 80, 'Processing Excel data structure...');

                        // Convert array format to object format
                        const headers = rawData[0];
                        const data = [];

                        for (let i = 1; i < rawData.length; i++) {
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header] = rawData[i][index] || '';
                            });
                            data.push(row);
                        }

                        updateProgressIndicator('parse', 100, 'Excel parsing complete');
                        console.log(`Successfully processed ${data.length} rows from Excel file`);

                        // Clear the array buffer to free memory
                        arrayBuffer = null;

                        setTimeout(() => analyzeDataFallback(data), 100);

                    } catch (conversionError) {
                        console.error('Excel conversion error:', conversionError);
                        hideProgressIndicators();
                        showErrorMessage(`Error converting Excel data: ${conversionError.message}. Try saving as CSV format for better compatibility.`);
                    }
                }, 100);

            } catch (readError) {
                console.error('Excel read error:', readError);
                hideProgressIndicators();

                let errorMessage = 'Error reading Excel file: ' + readError.message;

                if (readError.message.includes('Invalid array length') ||
                    readError.message.includes('out of memory')) {
                    errorMessage = `File too large for browser memory (${fileSizeMB.toFixed(1)}MB). Please try:\n• Using a smaller file\n• Converting to CSV format\n• Processing in smaller chunks`;
                }

                showErrorMessage(errorMessage);
            }
        }, 100);

    } catch (error) {
        console.error('Excel processing error:', error);
        hideProgressIndicators();
        showErrorMessage('Error processing Excel file: ' + error.message);
    }
}

function analyzeDataFallback(data) {
    try {
        console.log('Starting data analysis...', data.length, 'records');
        updateProgressIndicator('validate', 0, 'Validating data...');

        if (!data || data.length === 0) {
            throw new Error('No data found in file');
        }

        console.log('Data validation passed');
        updateProgressIndicator('validate', 100, 'Data validation complete');
        updateProgressIndicator('analyze', 0, 'Starting analysis...');

        const availableFields = Object.keys(data[0]);
        console.log('Available fields:', availableFields);

        const fieldAnalysis = {};

        // Initialize field analysis
        availableFields.forEach(field => {
            fieldAnalysis[field] = {
                totalRecords: data.length,
                blankCount: 0,
                blankPercentage: 0,
                riskLevel: 'LOW'
            };
        });

        const profileCompleteness = [];

        // For smaller datasets, process immediately
        if (data.length < 1000) {
            console.log('Processing small dataset immediately');

            data.forEach((row, index) => {
                availableFields.forEach(field => {
                    if (!row[field] ||
                        row[field].toString().trim() === '' ||
                        row[field].toString().toLowerCase() === 'null' ||
                        row[field].toString().toLowerCase() === 'undefined') {
                        fieldAnalysis[field].blankCount++;
                    }
                });

                // Calculate profile completeness
                const filledFields = availableFields.filter(field =>
                    row[field] &&
                    row[field].toString().trim() !== '' &&
                    row[field].toString().toLowerCase() !== 'null' &&
                    row[field].toString().toLowerCase() !== 'undefined'
                ).length;

                profileCompleteness.push((filledFields / availableFields.length) * 100);
            });

            console.log('Small dataset analysis complete');
            finalizeAnalysis();

        } else {
            // Process large datasets in chunks
            console.log('Processing large dataset in chunks');
            let currentIndex = 0;

            function analyzeChunk() {
                const chunkSize = 1000;
                const endIndex = Math.min(currentIndex + chunkSize, data.length);

                console.log(`Analyzing chunk: ${currentIndex} to ${endIndex}`);

                for (let i = currentIndex; i < endIndex; i++) {
                    const row = data[i];

                    availableFields.forEach(field => {
                        if (!row[field] ||
                            row[field].toString().trim() === '' ||
                            row[field].toString().toLowerCase() === 'null' ||
                            row[field].toString().toLowerCase() === 'undefined') {
                            fieldAnalysis[field].blankCount++;
                        }
                    });

                    // Calculate profile completeness
                    const filledFields = availableFields.filter(field =>
                        row[field] &&
                        row[field].toString().trim() !== '' &&
                        row[field].toString().toLowerCase() !== 'null' &&
                        row[field].toString().toLowerCase() !== 'undefined'
                    ).length;

                    profileCompleteness.push((filledFields / availableFields.length) * 100);
                }

                currentIndex = endIndex;
                const progress = Math.round((currentIndex / data.length) * 100);
                updateProgressIndicator('analyze', progress, `Analyzed ${currentIndex} of ${data.length} records`);

                if (currentIndex < data.length) {
                    setTimeout(analyzeChunk, 10);
                } else {
                    console.log('Large dataset analysis complete');
                    setTimeout(finalizeAnalysis, 100);
                }
            }

            setTimeout(analyzeChunk, 100);
        }

        function finalizeAnalysis() {
            console.log('Finalizing analysis...');

            // Calculate final statistics
            availableFields.forEach(field => {
                const analysis = fieldAnalysis[field];
                analysis.blankPercentage = parseFloat(((analysis.blankCount / data.length) * 100).toFixed(2));
                analysis.riskLevel = getRiskLevelFallback(field, analysis.blankPercentage);
            });

            const completeProfiles = profileCompleteness.filter(p => p === 100).length;
            const incompleteProfiles = data.length - completeProfiles;
            const completionRate = parseFloat(((completeProfiles / data.length) * 100).toFixed(1));

            updateProgressIndicator('analyze', 100, 'Analysis complete');

            // Handle results
            const results = {
                fieldAnalysis,
                profileCompleteness,
                totalRecords: data.length,
                completeProfiles,
                incompleteProfiles,
                completionRate,
                availableFields
            };

            console.log('Analysis results:', results);
            setTimeout(() => handleAnalysisComplete(results), 100);
        }

    } catch (error) {
        console.error('Analysis error:', error);
        hideProgressIndicators();
        showErrorMessage('Error analyzing data: ' + error.message);
    }
}

function getRiskLevelFallback(field, blankPercentage) {
    const isCritical = criticalFields.includes(field);

    if (isCritical) {
        if (blankPercentage > 10) return 'HIGH';
        if (blankPercentage > 5) return 'MEDIUM';
        return 'LOW';
    } else {
        if (blankPercentage > 25) return 'HIGH';
        if (blankPercentage > 15) return 'MEDIUM';
        return 'LOW';
    }
}

function parseCSVLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim().replace(/^"|"$/g, ''));
            current = '';
        } else {
            current += char;
        }
    }
    result.push(current.trim().replace(/^"|"$/g, ''));
    return result;
}

// Add processing timeout to prevent infinite loops
let processingTimeout;

function startProcessingTimeout() {
    // Clear any existing timeout
    if (processingTimeout) {
        clearTimeout(processingTimeout);
    }

    console.log('Starting processing timeout (60 seconds)');
    // Set 60 second timeout
    processingTimeout = setTimeout(() => {
        console.error('Processing timeout reached');
        hideProgressIndicators();
        showErrorMessage('Processing timeout. The file may be too large or complex. Please try with a smaller file or convert to CSV format.');
    }, 60000); // 60 seconds
}

function clearProcessingTimeout() {
    if (processingTimeout) {
        console.log('Clearing processing timeout');
        clearTimeout(processingTimeout);
        processingTimeout = null;
    }
}
