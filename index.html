<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Compliance Analytics | Enterprise Banking Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-shield-alt brand-icon"></i>
                <span class="brand-text">KYC Compliance Analytics</span>
            </div>
            <div class="nav-info">
                <span class="nav-timestamp" id="currentDateTime"></span>
                <div class="nav-user">
                    <i class="fas fa-user-circle"></i>
                    <span>Compliance Officer</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Container -->
    <div class="main-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-menu">
                <div class="menu-item active">
                    <i class="fas fa-chart-line"></i>
                    <span>Data Analysis</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-file-upload"></i>
                    <span>File Upload</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Risk Assessment</span>
                </div>
                <div class="menu-item">
                    <i class="fas fa-download"></i>
                    <span>Reports</span>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <h1>KYC Data Completeness Analysis</h1>
                    <p class="page-subtitle">Monitor and analyze customer data completeness for AML compliance</p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-secondary" onclick="exportToCSV()">
                        <i class="fas fa-file-csv"></i> Export CSV
                    </button>
                    <button class="btn btn-primary" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> Generate Report
                    </button>
                </div>
            </div>

            <!-- File Upload Section -->
            <div class="card upload-section">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-cloud-upload-alt"></i>
                        Dataset Upload
                    </h2>
                    <p class="card-subtitle">Upload your KYC dataset for compliance analysis</p>
                </div>
                <div class="card-body">
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">
                                <i class="fas fa-file-excel"></i>
                            </div>
                            <h3>Upload KYC Dataset</h3>
                            <p class="upload-description">
                                Drag and drop your Excel or CSV file here, or click to browse
                            </p>
                            <p class="upload-specs">
                                Supports: Excel (.xlsx, .xls), CSV files up to 200MB<br>
                                <small style="color: #6b7280;">💡 For large files (>50MB), CSV format is recommended for better performance</small>
                            </p>
                            <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" hidden>
                            <button class="btn btn-primary upload-btn" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open"></i>
                                Choose File
                            </button>
                        </div>
                    </div>

                    <!-- File Information -->
                    <div class="file-info-panel" id="fileInfo" style="display: none;">
                        <div class="file-details">
                            <div class="file-detail-item">
                                <span class="detail-label">File Name:</span>
                                <span class="detail-value" id="fileName"></span>
                            </div>
                            <div class="file-detail-item">
                                <span class="detail-label">File Size:</span>
                                <span class="detail-value" id="fileSize"></span>
                            </div>
                            <div class="file-detail-item">
                                <span class="detail-label">Records:</span>
                                <span class="detail-value" id="recordCount"></span>
                            </div>
                            <div class="file-detail-item">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value status-ready">
                                    <i class="fas fa-check-circle"></i> Ready for Analysis
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="card progress-section" id="progressSection" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-cog fa-spin"></i>
                        Processing Dataset
                    </h2>
                </div>
                <div class="card-body">
                    <div class="progress-container">
                        <div class="progress-stage" id="uploadStage">
                            <div class="stage-icon">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="stage-content">
                                <h4>File Upload</h4>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="uploadProgress"></div>
                                </div>
                                <span class="progress-text" id="uploadText">Preparing...</span>
                            </div>
                        </div>

                        <div class="progress-stage" id="parseStage">
                            <div class="stage-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stage-content">
                                <h4>Data Parsing</h4>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="parseProgress"></div>
                                </div>
                                <span class="progress-text" id="parseText">Waiting...</span>
                            </div>
                        </div>

                        <div class="progress-stage" id="validateStage">
                            <div class="stage-icon">
                                <i class="fas fa-check-double"></i>
                            </div>
                            <div class="stage-content">
                                <h4>Data Validation</h4>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="validateProgress"></div>
                                </div>
                                <span class="progress-text" id="validateText">Waiting...</span>
                            </div>
                        </div>

                        <div class="progress-stage" id="analyzeStage">
                            <div class="stage-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="stage-content">
                                <h4>Analysis & Visualization</h4>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="analyzeProgress"></div>
                                </div>
                                <span class="progress-text" id="analyzeText">Waiting...</span>
                            </div>
                        </div>
                    </div>

                    <div class="overall-progress">
                        <div class="overall-progress-bar">
                            <div class="overall-progress-fill" id="overallProgress"></div>
                        </div>
                        <div class="progress-info">
                            <span id="overallProgressText">Initializing...</span>
                            <span id="estimatedTime"></span>
                        </div>
                    </div>

                    <!-- Debug Controls -->
                    <div class="debug-controls">
                        <button id="toggleDebug" class="btn btn-secondary" onclick="toggleDebugConsole()">
                            <i class="fas fa-bug"></i> Show Debug Console
                        </button>
                        <button id="pauseProcessing" class="btn btn-warning" onclick="pauseProcessing()" style="display: none;">
                            <i class="fas fa-pause"></i> Pause Processing
                        </button>
                    </div>
                </div>
            </div>

            <!-- Debug Console -->
            <div class="card debug-section" id="debugSection" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-bug"></i>
                        Debug Console
                    </h2>
                    <div class="debug-header-controls">
                        <button onclick="clearDebugLog()" class="btn btn-sm btn-secondary">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                        <button onclick="exportDebugLog()" class="btn btn-sm btn-primary">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="debug-stats">
                        <div class="debug-stat">
                            <span class="stat-label">Memory Usage:</span>
                            <span class="stat-value" id="memoryUsage">Calculating...</span>
                        </div>
                        <div class="debug-stat">
                            <span class="stat-label">Processing Time:</span>
                            <span class="stat-value" id="processingTime">0s</span>
                        </div>
                        <div class="debug-stat">
                            <span class="stat-label">Records/sec:</span>
                            <span class="stat-value" id="processingSpeed">0</span>
                        </div>
                    </div>
                    <div id="debugLog" class="debug-log"></div>
                </div>
            </div>

            <!-- Analysis Dashboard -->
            <div class="analysis-section" id="analysisSection" style="display: none;">
                <!-- Key Metrics -->
                <div class="metrics-grid">
                    <div class="metric-card primary">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="metric-trend positive">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-label">Total Records</h3>
                            <div class="metric-value" id="totalRecords">0</div>
                            <p class="metric-subtitle">Customer profiles analyzed</p>
                        </div>
                    </div>

                    <div class="metric-card success">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-trend positive">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-label">Complete Profiles</h3>
                            <div class="metric-value" id="completeProfiles">0</div>
                            <p class="metric-subtitle">Fully compliant records</p>
                        </div>
                    </div>

                    <div class="metric-card warning">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-trend negative">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-label">Incomplete Profiles</h3>
                            <div class="metric-value" id="incompleteProfiles">0</div>
                            <p class="metric-subtitle">Require attention</p>
                        </div>
                    </div>

                    <div class="metric-card info">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="metric-trend" id="completionTrend">
                                <i class="fas fa-minus"></i>
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-label">Completion Rate</h3>
                            <div class="metric-value" id="completionRate">0%</div>
                            <p class="metric-subtitle">Overall compliance score</p>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-grid">
                    <div class="card chart-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-bar"></i>
                                Missing Data by Field
                            </h3>
                            <div class="chart-controls">
                                <button class="btn btn-sm btn-outline" onclick="refreshChart('missing')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="missingDataChart"></canvas>
                        </div>
                    </div>

                    <div class="card chart-card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                Profile Completeness Distribution
                            </h3>
                            <div class="chart-controls">
                                <button class="btn btn-sm btn-outline" onclick="refreshChart('completeness')">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="completenessChart"></canvas>
                        </div>
                    </div>

                    <div class="card chart-card">
                        <div class="card-header">
                            <h3>Address Field Analysis</h3>
                            <div class="card-actions">
                                <button class="btn-icon" onclick="window.addressChart && window.addressChart.update()" title="Refresh Chart">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="addressChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Detailed Analysis Table -->
                <div class="card table-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-table"></i>
                            Detailed Field Analysis
                        </h3>
                        <div class="table-controls">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="Search fields..." id="fieldSearch">
                            </div>
                            <select class="filter-select" id="riskFilter">
                                <option value="">All Risk Levels</option>
                                <option value="HIGH">High Risk</option>
                                <option value="MEDIUM">Medium Risk</option>
                                <option value="LOW">Low Risk</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table id="fieldAnalysisTable" class="data-table">
                                <thead>
                                    <tr>
                                        <th class="sortable" data-sort="field">
                                            Field Name <i class="fas fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-sort="total">
                                            Total Records <i class="fas fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-sort="blank">
                                            Blank Count <i class="fas fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-sort="percentage">
                                            Blank % <i class="fas fa-sort"></i>
                                        </th>
                                        <th class="sortable" data-sort="risk">
                                            Risk Level <i class="fas fa-sort"></i>
                                        </th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="fieldAnalysisBody">
                                </tbody>
                            </table>
                        </div>
                        <div class="table-pagination" id="tablePagination">
                            <div class="pagination-info">
                                Showing <span id="paginationStart">0</span> to <span id="paginationEnd">0</span> of <span id="paginationTotal">0</span> fields
                            </div>
                            <div class="pagination-controls">
                                <button class="btn btn-sm" id="prevPage" onclick="changePage(-1)">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </button>
                                <span class="page-numbers" id="pageNumbers"></span>
                                <button class="btn btn-sm" id="nextPage" onclick="changePage(1)">
                                    Next <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment -->
                <div class="card risk-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-shield-alt"></i>
                            AML Risk Assessment
                        </h3>
                        <div class="card-actions">
                            <button class="btn-primary" onclick="recalculateWithNewAddressCriteria()" title="Recalculate with Enhanced Address Validation">
                                <i class="fas fa-sync-alt"></i>
                                Recalculate Analysis
                            </button>
                        </div>
                        <div class="risk-summary">
                            <span class="risk-indicator high" id="highRiskCount">0</span>
                            <span class="risk-indicator medium" id="mediumRiskCount">0</span>
                            <span class="risk-indicator low" id="lowRiskCount">0</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="risk-grid" id="riskGrid">
                        </div>
                    </div>
                </div>

                <!-- Export Section -->
                <div class="card export-card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-download"></i>
                            Export & Reports
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="export-grid">
                            <button class="export-option" onclick="exportToCSV()">
                                <div class="export-icon">
                                    <i class="fas fa-file-csv"></i>
                                </div>
                                <div class="export-content">
                                    <h4>Analysis Report (CSV)</h4>
                                    <p>Export detailed field analysis data</p>
                                </div>
                            </button>

                            <button class="export-option" onclick="exportToPDF()">
                                <div class="export-icon">
                                    <i class="fas fa-file-pdf"></i>
                                </div>
                                <div class="export-content">
                                    <h4>Compliance Report (PDF)</h4>
                                    <p>Generate comprehensive compliance report</p>
                                </div>
                            </button>

                            <button class="export-option" onclick="exportIncompleteRecords()">
                                <div class="export-icon">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <div class="export-content">
                                    <h4>Incomplete Records (CSV)</h4>
                                    <p>Export records requiring attention</p>
                                </div>
                            </button>

                            <button class="export-option" onclick="exportSummaryReport()">
                                <div class="export-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="export-content">
                                    <h4>Executive Summary</h4>
                                    <p>High-level compliance overview</p>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
                <div class="spinner-ring"></div>
            </div>
            <h3 id="loadingTitle">Processing Dataset</h3>
            <p id="loadingMessage">Please wait while we analyze your data...</p>
        </div>
    </div>

    <!-- Web Worker for large file processing -->
    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Initialize datetime display
        function updateDateTime() {
            const now = new Date();
            document.getElementById('currentDateTime').textContent =
                now.toLocaleDateString() + ' ' + now.toLocaleTimeString();
        }
        updateDateTime();
        setInterval(updateDateTime, 1000);
    </script>

    <script src="script.js"></script>
</body>
</html>
