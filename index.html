<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KYC Data Analysis - Blank Field Detection</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>KYC Data Analysis Tool</h1>
            <p class="subtitle">Analyze blank fields for AML compliance assessment</p>
        </header>

        <div class="upload-section">
            <div class="upload-card">
                <h2>Upload Your KYC Dataset</h2>
                <div class="file-upload-area" id="fileUploadArea">
                    <div class="upload-icon">📊</div>
                    <p>Drag and drop your Excel/CSV file here or click to browse</p>
                    <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" hidden>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        Choose File
                    </button>
                </div>
                <div class="file-info" id="fileInfo" style="display: none;">
                    <p><strong>File:</strong> <span id="fileName"></span></p>
                    <p><strong>Size:</strong> <span id="fileSize"></span></p>
                    <p><strong>Records:</strong> <span id="recordCount"></span></p>
                </div>
            </div>
        </div>

        <div class="analysis-section" id="analysisSection" style="display: none;">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Records</h3>
                    <div class="stat-value" id="totalRecords">0</div>
                </div>
                <div class="stat-card">
                    <h3>Complete Profiles</h3>
                    <div class="stat-value" id="completeProfiles">0</div>
                </div>
                <div class="stat-card">
                    <h3>Incomplete Profiles</h3>
                    <div class="stat-value" id="incompleteProfiles">0</div>
                </div>
                <div class="stat-card">
                    <h3>Completion Rate</h3>
                    <div class="stat-value" id="completionRate">0%</div>
                </div>
            </div>

            <div class="charts-section">
                <div class="chart-container">
                    <h3>Missing Data by Field</h3>
                    <canvas id="missingDataChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Profile Completeness Distribution</h3>
                    <canvas id="completenessChart"></canvas>
                </div>
            </div>

            <div class="detailed-analysis">
                <h3>Detailed Field Analysis</h3>
                <div class="table-container">
                    <table id="fieldAnalysisTable">
                        <thead>
                            <tr>
                                <th>Field Name</th>
                                <th>Total Records</th>
                                <th>Blank Count</th>
                                <th>Blank Percentage</th>
                                <th>Risk Level</th>
                            </tr>
                        </thead>
                        <tbody id="fieldAnalysisBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="risk-assessment">
                <h3>AML Risk Assessment</h3>
                <div class="risk-grid" id="riskGrid">
                </div>
            </div>

            <div class="export-section">
                <h3>Export Results</h3>
                <div class="export-buttons">
                    <button class="export-btn" onclick="exportToCSV()">Export to CSV</button>
                    <button class="export-btn" onclick="exportToPDF()">Generate Report (PDF)</button>
                    <button class="export-btn" onclick="exportIncompleteRecords()">Export Incomplete Records</button>
                </div>
            </div>
        </div>

        <div class="loading" id="loading" style="display: none;">
            <div class="spinner"></div>
            <p>Analyzing your data...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
