/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Banking Color Palette */
    --primary-blue: #1e3a8a;
    --secondary-blue: #3b82f6;
    --light-blue: #dbeafe;
    --dark-blue: #1e40af;

    --success-green: #059669;
    --light-green: #d1fae5;

    --warning-orange: #d97706;
    --light-orange: #fed7aa;

    --danger-red: #dc2626;
    --light-red: #fecaca;

    --neutral-gray: #6b7280;
    --light-gray: #f9fafb;
    --medium-gray: #e5e7eb;
    --dark-gray: #374151;

    --white: #ffffff;
    --black: #111827;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;

    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
}

body {
    font-family: var(--font-family);
    background-color: var(--light-gray);
    color: var(--black);
    line-height: 1.6;
    font-size: var(--font-size-base);
}

/* Navigation */
.navbar {
    background: var(--white);
    border-bottom: 1px solid var(--medium-gray);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.brand-icon {
    color: var(--primary-blue);
    font-size: var(--font-size-xl);
}

.brand-text {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--black);
}

.nav-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
}

.nav-timestamp {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--dark-gray);
    font-size: var(--font-size-sm);
}

/* Main Layout */
.main-container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 64px);
}

/* Sidebar */
.sidebar {
    width: 240px;
    background: var(--white);
    border-right: 1px solid var(--medium-gray);
    padding: var(--spacing-6) 0;
}

.sidebar-menu {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    padding: 0 var(--spacing-4);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-md);
    color: var(--neutral-gray);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-sm);
}

.menu-item:hover {
    background-color: var(--light-blue);
    color: var(--primary-blue);
}

.menu-item.active {
    background-color: var(--primary-blue);
    color: var(--white);
}

.menu-item i {
    width: 16px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-6);
    overflow-y: auto;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--medium-gray);
}

.page-title h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--black);
    margin-bottom: var(--spacing-2);
}

.page-subtitle {
    font-size: var(--font-size-base);
    color: var(--neutral-gray);
}

.page-actions {
    display: flex;
    gap: var(--spacing-3);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

.btn-primary:hover {
    background-color: var(--dark-blue);
    border-color: var(--dark-blue);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.btn-secondary:hover {
    background-color: var(--light-blue);
}

.btn-outline {
    background-color: transparent;
    color: var(--neutral-gray);
    border-color: var(--medium-gray);
}

.btn-outline:hover {
    background-color: var(--light-gray);
    color: var(--dark-gray);
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-6);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--medium-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--black);
    margin: 0;
}

.card-title i {
    color: var(--primary-blue);
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
    margin-top: var(--spacing-1);
}

.card-body {
    padding: var(--spacing-6);
}

/* Upload Section */
.upload-section {
    margin-bottom: var(--spacing-8);
}

/* File Upload Area */
.file-upload-area {
    border: 2px dashed var(--medium-gray);
    border-radius: var(--radius-lg);
    padding: var(--spacing-12);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: var(--light-gray);
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: var(--primary-blue);
    background-color: var(--light-blue);
    transform: scale(1.01);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-4);
}

.upload-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.upload-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--black);
}

.upload-description {
    font-size: var(--font-size-base);
    color: var(--neutral-gray);
    max-width: 400px;
}

.upload-specs {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
    font-style: italic;
}

/* File Info Panel */
.file-info-panel {
    background: var(--light-blue);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    margin-top: var(--spacing-6);
}

.file-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.file-detail-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.detail-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--neutral-gray);
}

.detail-value {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--black);
}

.status-ready {
    color: var(--success-green) !important;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

/* Progress Section */
.progress-section {
    margin-bottom: var(--spacing-8);
}

.progress-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.progress-stage {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
    background: var(--light-gray);
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
}

.progress-stage.active {
    background: var(--light-blue);
    border-left: 4px solid var(--primary-blue);
}

.progress-stage.completed {
    background: var(--light-green);
    border-left: 4px solid var(--success-green);
}

.stage-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--medium-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
}

.progress-stage.active .stage-icon {
    background: var(--primary-blue);
    animation: pulse 2s infinite;
}

.progress-stage.completed .stage-icon {
    background: var(--success-green);
}

.stage-content {
    flex: 1;
}

.stage-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--black);
    margin-bottom: var(--spacing-2);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--medium-gray);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-2);
}

.progress-fill {
    height: 100%;
    background: var(--primary-blue);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-stage.completed .progress-fill {
    background: var(--success-green);
    width: 100%;
}

.progress-text {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

.overall-progress {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    border: 1px solid var(--medium-gray);
}

.overall-progress-bar {
    width: 100%;
    height: 12px;
    background: var(--medium-gray);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: var(--spacing-4);
}

.overall-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
    border-radius: var(--radius-md);
    transition: width 0.5s ease;
    width: 0%;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Analysis Section */
.analysis-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.metric-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-blue);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-card.primary {
    border-left-color: var(--primary-blue);
}

.metric-card.success {
    border-left-color: var(--success-green);
}

.metric-card.warning {
    border-left-color: var(--warning-orange);
}

.metric-card.info {
    border-left-color: var(--secondary-blue);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
}

.metric-card.primary .metric-icon {
    background: var(--primary-blue);
}

.metric-card.success .metric-icon {
    background: var(--success-green);
}

.metric-card.warning .metric-icon {
    background: var(--warning-orange);
}

.metric-card.info .metric-icon {
    background: var(--secondary-blue);
}

.metric-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-size: var(--font-size-sm);
}

.metric-trend.positive {
    background: var(--light-green);
    color: var(--success-green);
}

.metric-trend.negative {
    background: var(--light-red);
    color: var(--danger-red);
}

.metric-content {
    text-align: left;
}

.metric-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--neutral-gray);
    margin-bottom: var(--spacing-2);
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--black);
    margin-bottom: var(--spacing-1);
    animation: countUp 1s ease-out;
}

.metric-subtitle {
    font-size: var(--font-size-xs);
    color: var(--neutral-gray);
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Charts Section */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.chart-card {
    min-height: 400px;
}

.chart-controls {
    display: flex;
    gap: var(--spacing-2);
}

.chart-card .card-body {
    position: relative;
    height: 350px;
}

.chart-card canvas {
    max-height: 100%;
}

/* Table Styles */
.table-card {
    margin-bottom: var(--spacing-8);
}

.table-controls {
    display: flex;
    gap: var(--spacing-4);
    align-items: center;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--spacing-3);
    color: var(--neutral-gray);
    font-size: var(--font-size-sm);
}

.search-box input {
    padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-8);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    width: 250px;
}

.filter-select {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    background: var(--white);
}

.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    border: 1px solid var(--medium-gray);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    font-size: var(--font-size-sm);
}

.data-table th,
.data-table td {
    padding: var(--spacing-4);
    text-align: left;
    border-bottom: 1px solid var(--medium-gray);
}

.data-table th {
    background: var(--light-gray);
    font-weight: 600;
    color: var(--black);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.data-table th.sortable:hover {
    background: var(--medium-gray);
}

.data-table th.sortable i {
    margin-left: var(--spacing-2);
    opacity: 0.5;
}

.data-table tbody tr:hover {
    background: var(--light-gray);
}

.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4);
    border-top: 1px solid var(--medium-gray);
    background: var(--light-gray);
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.page-numbers {
    display: flex;
    gap: var(--spacing-1);
}

.page-number {
    padding: var(--spacing-1) var(--spacing-2);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-sm);
    background: var(--white);
    color: var(--neutral-gray);
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: all 0.2s ease;
}

.page-number:hover,
.page-number.active {
    background: var(--primary-blue);
    color: var(--white);
    border-color: var(--primary-blue);
}

/* Risk Assessment Styles */
.risk-high {
    background-color: var(--light-red);
    color: var(--danger-red);
}

.risk-medium {
    background-color: var(--light-orange);
    color: var(--warning-orange);
}

.risk-low {
    background-color: var(--light-green);
    color: var(--success-green);
}

.risk-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.risk-summary {
    display: flex;
    gap: var(--spacing-3);
}

.risk-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.risk-indicator.high {
    background: var(--light-red);
    color: var(--danger-red);
}

.risk-indicator.medium {
    background: var(--light-orange);
    color: var(--warning-orange);
}

.risk-indicator.low {
    background: var(--light-green);
    color: var(--success-green);
}

.risk-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-6);
}

.risk-assessment-item {
    background: var(--light-gray);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    border-left: 4px solid var(--primary-blue);
}

.risk-assessment-item.high-risk {
    border-left-color: var(--danger-red);
    background: var(--light-red);
}

.risk-assessment-item.medium-risk {
    border-left-color: var(--warning-orange);
    background: var(--light-orange);
}

.risk-assessment-item.low-risk {
    border-left-color: var(--success-green);
    background: var(--light-green);
}

.risk-assessment-item h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-base);
    font-weight: 600;
}

.risk-assessment-item ul {
    list-style: none;
    padding: 0;
}

.risk-assessment-item li {
    padding: var(--spacing-2) 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.risk-assessment-item li:last-child {
    border-bottom: none;
}

/* Export Section */
.export-card {
    margin-bottom: var(--spacing-8);
}

.export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-4);
}

.export-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    padding: var(--spacing-6);
    background: var(--light-gray);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.export-option:hover {
    background: var(--light-blue);
    border-color: var(--primary-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-lg);
}

.export-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--black);
    margin-bottom: var(--spacing-1);
}

.export-content p {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-content {
    text-align: center;
    color: var(--white);
    max-width: 400px;
    padding: var(--spacing-8);
}

.loading-spinner {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-6);
}

.spinner-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    width: 60px;
    height: 60px;
    top: 10px;
    left: 10px;
    border-top-color: var(--secondary-blue);
    animation-duration: 1.5s;
    animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
    width: 40px;
    height: 40px;
    top: 20px;
    left: 20px;
    border-top-color: var(--primary-blue);
    animation-duration: 2s;
}

.loading-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

.loading-content p {
    font-size: var(--font-size-base);
    opacity: 0.9;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Risk Badges */
.risk-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-badge.risk-high {
    background-color: var(--light-red);
    color: var(--danger-red);
}

.risk-badge.risk-medium {
    background-color: var(--light-orange);
    color: var(--warning-orange);
}

.risk-badge.risk-low {
    background-color: var(--light-green);
    color: var(--success-green);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }

.hidden { display: none; }
.visible { display: block; }

/* Print Styles */
@media print {
    .navbar,
    .sidebar,
    .page-actions,
    .chart-controls,
    .table-controls,
    .export-card,
    .no-print {
        display: none !important;
    }

    .main-container {
        display: block;
    }

    .main-content {
        padding: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid var(--medium-gray);
        break-inside: avoid;
        margin-bottom: var(--spacing-4);
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-container {
        max-width: 100%;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 200px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-4);
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-3);
    }

    .search-box input {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        padding: var(--spacing-4);
    }

    .sidebar-menu {
        flex-direction: row;
        overflow-x: auto;
        padding: 0;
        gap: var(--spacing-2);
    }

    .menu-item {
        white-space: nowrap;
        min-width: fit-content;
    }

    .main-content {
        padding: var(--spacing-4);
    }

    .nav-container {
        padding: 0 var(--spacing-4);
    }

    .nav-info {
        gap: var(--spacing-3);
    }

    .nav-timestamp {
        display: none;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .export-grid {
        grid-template-columns: 1fr;
    }

    .risk-grid {
        grid-template-columns: 1fr;
    }

    .file-details {
        grid-template-columns: 1fr;
    }

    .progress-container {
        gap: var(--spacing-4);
    }

    .progress-stage {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }

    .stage-content {
        width: 100%;
    }

    .data-table {
        font-size: var(--font-size-xs);
    }

    .data-table th,
    .data-table td {
        padding: var(--spacing-2) var(--spacing-3);
    }

    .table-pagination {
        flex-direction: column;
        gap: var(--spacing-3);
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    .risk-assessment-item li {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 var(--spacing-3);
    }

    .brand-text {
        display: none;
    }

    .main-content {
        padding: var(--spacing-3);
    }

    .card-header,
    .card-body {
        padding: var(--spacing-4);
    }

    .page-title h1 {
        font-size: var(--font-size-2xl);
    }

    .metric-value {
        font-size: var(--font-size-2xl);
    }

    .upload-content {
        gap: var(--spacing-3);
    }

    .upload-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .loading-content {
        padding: var(--spacing-6);
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
    }

    .spinner-ring:nth-child(2) {
        width: 45px;
        height: 45px;
        top: 7.5px;
        left: 7.5px;
    }

    .spinner-ring:nth-child(3) {
        width: 30px;
        height: 30px;
        top: 15px;
        left: 15px;
    }
}
