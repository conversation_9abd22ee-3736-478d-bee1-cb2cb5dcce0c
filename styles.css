* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.upload-section {
    margin-bottom: 40px;
}

.upload-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
}

.file-upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 60px 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #764ba2;
    background-color: #f8f9ff;
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    margin-top: 20px;
    transition: transform 0.2s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
}

.file-info {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
    text-align: left;
}

.analysis-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card h3 {
    font-size: 0.9rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
}

.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.chart-container {
    background: #f8f9ff;
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #e0e6ff;
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.detailed-analysis {
    margin-bottom: 40px;
}

.detailed-analysis h3 {
    margin-bottom: 20px;
    color: #333;
}

.table-container {
    overflow-x: auto;
    border-radius: 10px;
    border: 1px solid #e0e6ff;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e0e6ff;
}

th {
    background: #f8f9ff;
    font-weight: 600;
    color: #333;
}

.risk-high {
    background-color: #ffebee;
    color: #c62828;
}

.risk-medium {
    background-color: #fff3e0;
    color: #ef6c00;
}

.risk-low {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.risk-assessment {
    margin-bottom: 40px;
}

.risk-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.risk-card {
    padding: 20px;
    border-radius: 10px;
    border-left: 5px solid;
}

.export-section {
    text-align: center;
}

.export-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.export-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.export-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

.loading {
    text-align: center;
    color: white;
    padding: 60px;
}

.spinner {
    border: 4px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top: 4px solid white;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Risk badges */
.risk-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.risk-badge.risk-high {
    background-color: #ffcdd2;
    color: #c62828;
}

.risk-badge.risk-medium {
    background-color: #ffe0b2;
    color: #ef6c00;
}

.risk-badge.risk-low {
    background-color: #c8e6c9;
    color: #2e7d32;
}

/* Table enhancements */
tbody tr:hover {
    background-color: #f8f9ff;
}

.risk-card h4 {
    display: flex;
    align-items: center;
    gap: 8px;
}

.risk-card ul {
    list-style-type: none;
    padding-left: 0;
}

.risk-card li {
    padding: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* File upload enhancements */
.file-upload-area.dragover {
    border-color: #764ba2;
    background-color: #f8f9ff;
    transform: scale(1.02);
}

/* Animation for statistics */
.stat-value {
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print styles for reports */
@media print {
    body {
        background: white;
        color: black;
    }

    .no-print {
        display: none;
    }

    .chart-container {
        break-inside: avoid;
    }
}

@media (max-width: 768px) {
    .charts-section {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .export-buttons {
        flex-direction: column;
        align-items: center;
    }

    .risk-card li {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    table {
        font-size: 0.9rem;
    }

    th, td {
        padding: 10px 8px;
    }
}
